# Play Store Monitor - 应用状态监控系统

## 概述

Play Store Monitor 是一个基于 Cloudflare Workers 的应用状态监控系统，专门用于跟踪 Google Play Store 上应用的状态变化。系统会自动检测应用是否在架，并在状态变化时发送通知到飞书（Lark）。

## 主要功能

1. **自动监控**：每小时自动检查应用状态
2. **状态变化通知**：当应用下架或重新上架时发送飞书通知
3. **应用管理**：通过API添加/移除监控的应用
4. **历史记录**：存储应用的详细信息和状态变化历史
5. **错误处理**：自动重试和错误日志记录

## 技术栈

- **Cloudflare Workers**：无服务器运行环境
- **D1 Database**：Cloudflare的关系型数据库
- **KV存储**：用于存储应用配置信息
- **飞书Webhook**：状态变化通知

## 快速开始

### 前提条件

1. Cloudflare 账户
2. 飞书群组（用于接收通知）
3. Play Store API 访问密钥

### 安装步骤

```bash
# 克隆仓库
git clone https://github.com/your-repo/play-store-monitor.git
cd play-store-monitor

# 安装依赖
npm install

# 配置环境变量（创建 .dev.vars 文件）
echo "API_KEY=your_play_store_api_key" > .dev.vars
echo "MANUAL_TRIGGER_KEY=your_secure_trigger_key" >> .dev.vars
echo "LARK_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_token" >> .dev.vars

# 创建D1数据库
wrangler d1 create play-store-db

# 初始化数据库表
wrangler d1 execute play-store-db --file=./schema.sql

# 部署到Cloudflare
wrangler deploy
```

## API 端点

### 1. 触发监控任务
- **URL**: `/trigger`
- **方法**: GET
- **认证**: Bearer Token
- **响应**: `监测任务已启动`

### 2. 应用管理
- **URL**: `/manage`
- **方法**:
  - GET: 获取所有跟踪应用
  - POST: 添加新应用
  - DELETE: 移除应用

#### 添加应用示例
```json
POST /manage
Content-Type: application/json
Authorization: Bearer your_secure_trigger_key

{
  "package_name": "com.example.app",
  "app_name": "Example App"
}
```

### 3. 测试飞书通知
- **URL**: `/test-lark`
- **方法**: GET
- **认证**: Bearer Token
- **响应**: 发送测试通知

## 数据库结构

### `app_status` 表结构

| 字段名 | 类型 | 描述 |
|--------|------|------|
| package_name | TEXT (PK) | 应用包名 |
| title | TEXT | 应用标题 |
| appId | TEXT | 应用ID |
| devId | TEXT | 开发者ID |
| developerId | TEXT | 开发者ID |
| developerEmail | TEXT | 开发者邮箱 |
| developerWebsite | TEXT | 开发者网站 |
| developerLegalName | TEXT | 开发者法定名称 |
| developerLegalEmail | TEXT | 开发者法定邮箱 |
| developerLegalAddress | TEXT | 开发者法定地址 |
| developerLegalPhoneNumber | TEXT | 开发者联系电话 |
| developerInternalID | TEXT | 开发者内部ID |
| isAlive | BOOLEAN | 是否在架 |
| lastCheck | TEXT | 最后检查时间 |
| belongUs | BOOLEAN | 是否属于我们 |
| downTime | TEXT | 下架时间 |
| released | TEXT | 上架时间 |

## 定时任务

系统配置了每小时执行一次的定时任务：
```json
{
	"triggers": {
		"crons": ["0 * * * *"]
	}
}
```

## 开发指南

### 本地开发
```bash
wrangler dev
```

### 测试
```bash
# 运行单元测试
npm test

# 测试添加应用
curl -X POST http://localhost:8787/manage \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_secure_trigger_key" \
  -d '{"package_name":"com.example.app","app_name":"Example App"}'
```

### 生产部署
```bash
wrangler deploy --env production
```

## 贡献指南

欢迎提交 issue 和 pull request：
1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/your-feature`)
3. 提交更改 (`git commit -am 'Add some feature'`)
4. 推送到分支 (`git push origin feature/your-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT License。

### 数据库初始化脚本 (schema.sql)

请将以下内容保存为 `schema.sql` 文件：

```sql
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS app_status (
    package_name TEXT PRIMARY KEY NOT NULL,
    title TEXT,
    appId TEXT,
    devId TEXT,
    developerId TEXT,
    developerEmail TEXT,
    developerWebsite TEXT,
    developerLegalName TEXT,
    developerLegalEmail TEXT,
    developerLegalAddress TEXT,
    developerLegalPhoneNumber TEXT,
    developerInternalID TEXT,
    isAlive BOOLEAN NOT NULL,
    lastCheck TEXT NOT NULL,
    belongUs BOOLEAN NOT NULL DEFAULT 0,
    downTime TEXT
);
CREATE INDEX idx_isAlive ON app_status (isAlive);
CREATE INDEX idx_lastCheck ON app_status (lastCheck);
CREATE INDEX idx_downTime ON app_status (downTime);
COMMIT;
```
