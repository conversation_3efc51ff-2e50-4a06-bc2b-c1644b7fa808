export interface AppConfig {
    package_name: string;
    app_name: string;
}

export interface PlayStoreResponse {
    status: 'available' | 'unavailable';
    title?: string;
    appId?: string;
    developer?: {
        devId?: string;
    };
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    playstoreUrl?: string;
    description?: string;
    installs?: string;
    minInstalls?: number;
    maxInstalls?: number;
}
