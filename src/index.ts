import type {
    ScheduledEvent,
    ExecutionContext,
    D1PreparedStatement,
    D1Result
} from '@cloudflare/workers-types';

interface Env {
    APPS_STORE_LIST_AND: KVNamespace;
    DB: D1Database;
    API_KEY: string;
    MANUAL_TRIGGER_KEY: string;
    LARK_WEBHOOK_URL: string;
}

interface AppConfig {
    package_name: string;
    app_name: string;
}

interface PlayStoreResponse {
    status: 'available' | 'unavailable';
    title?: string;
    appId?: string;
    developer?: {
        devId?: string;
    };
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    playstoreUrl?: string;
    description?: string;
    installs?: string;
    minInstalls?: number;
    maxInstalls?: number;
	released?: string;
}

export default {
    async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
        const url = new URL(request.url);

        // 添加健康检查端点
        if (url.pathname === '/health') {
            try {
                const result = await env.DB.prepare("SELECT 1 AS status").first();
                return Response.json({ status: 'ok', db: result?.status === 1 });
            } catch (err) {
                return Response.json({ status: 'error', message: err.message }, { status: 500 });
            }
        }

        if (url.pathname === '/trigger') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            ctx.waitUntil(executeMonitor(env));
            return new Response('监测任务已启动');
        }

        // 新增管理接口
        if (url.pathname === '/manage') {
            const auth = request.headers.get('Authorization');
            if (auth !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            // 处理应用列表管理
            if (request.method === 'GET') {
                const apps = await getAllTrackedApps(env);
                return Response.json(apps);
            }

            if (request.method === 'POST') {
                const body = await request.json<AppConfig>();
                await addTrackedApp(env, body);
                return new Response('应用已添加');
            }

            if (request.method === 'DELETE') {
                const { package_name } = await request.json<{ package_name: string }>();
                await removeTrackedApp(env, package_name);
                return new Response('应用已移除');
            }
        }

        // 添加测试端点
        if (url.pathname === '/test-lark') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            try {
                await sendLarkAlert(env,
                    { package_name: 'test.package', app_name: 'Test App' },
                    '这是一条测试消息\n包含多行内容\n用于测试消息发送');
                return new Response('Test message sent successfully');
            } catch (error) {
                return new Response(`Failed to send test message: ${error.message}`, { status: 500 });
            }
        }

        return new Response(`管理端点:
GET /manage - 获取跟踪应用列表
POST /manage - 添加应用 {package_name, app_name}
DELETE /manage - 移除应用 {package_name}`);
    },

    async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
        ctx.waitUntil(executeMonitor(env));
    }
};

// 安全执行D1数据库操作（修复版）
async function d1SafeRun(query: D1PreparedStatement): Promise<D1Result> {
    try {
        const result = await query.run();

        // 添加详细的日志记录
        console.log('D1 操作结果:', {
            success: result.success,
            meta: result.meta,
            error: result.error,
            sql: query.query
        });

        if (!result.success) {
            throw new Error(result.error || 'D1 操作失败');
        }

        return result;
    } catch (err) {
        console.error('数据库操作失败:', {
            message: err.message,
            stack: err.stack,
            sql: query.query
        });
        throw new Error('数据库操作失败');
    }
}

// 安全截断字段到指定长度
function truncateField(value: string | null | undefined, maxLength: number): string | null {
    return value ? value.substring(0, maxLength) : null;
}

// 应用列表管理函数
async function getAllTrackedApps(env: Env): Promise<AppConfig[]> {
    console.log('获取所有跟踪应用');
    const list = await env.APPS_STORE_LIST_AND.list();
    const apps: AppConfig[] = [];

    for (const key of list.keys) {
        const app = await env.APPS_STORE_LIST_AND.get<AppConfig>(key.name, 'json');
        if (app) apps.push(app);
    }

    return apps;
}

async function addTrackedApp(env: Env, app: AppConfig) {
    console.log(`添加应用: ${app.package_name}`);

    // 存储应用到APPS_STORE_LIST_AND
    await env.APPS_STORE_LIST_AND.put(app.package_name, JSON.stringify(app));

    const now = new Date().toISOString();
    try {
        console.log('获取应用详情');
        const details = await checkPlayStore(env.API_KEY, app.package_name);

        // 确定初始状态
        const isAlive = details.status === 'available';

        // 准备要插入的数据字段，使用 truncateField 确保长度
        const insertData = {
            package_name: app.package_name,
            title: truncateField(details.title, 255),
            appId: truncateField(details.appId, 100),
            devId: truncateField(details.developer?.devId, 100),
            developerId: truncateField(details.developerId, 100),
            developerEmail: truncateField(details.developerEmail, 255),
            developerWebsite: truncateField(details.developerWebsite, 255),
            developerLegalName: truncateField(details.developerLegalName, 255),
            developerLegalEmail: truncateField(details.developerLegalEmail, 255),
            developerLegalAddress: truncateField(details.developerLegalAddress, 500),
            developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
            developerInternalID: truncateField(details.developerInternalID, 100),
            isAlive: isAlive ? 1 : 0,
            lastCheck: now,
            belongUs: 0,
            downTime: isAlive ? null : now,
            released: truncateField(details.released, 50)
        };

        // 构建SQL插入语句
        const insertQuery = env.DB.prepare(`
            INSERT INTO app_status (
                package_name, title, appId, devId, developerId,
                developerEmail, developerWebsite, developerLegalName,
                developerLegalEmail, developerLegalAddress, developerLegalPhoneNumber,
                developerInternalID, isAlive, lastCheck, belongUs, downTime, released
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
            insertData.package_name,
            insertData.title,
            insertData.appId,
            insertData.devId,
            insertData.developerId,
            insertData.developerEmail,
            insertData.developerWebsite,
            insertData.developerLegalName,
            insertData.developerLegalEmail,
            insertData.developerLegalAddress,
            insertData.developerLegalPhoneNumber,
            insertData.developerInternalID,
            insertData.isAlive,
            insertData.lastCheck,
            insertData.belongUs,
            insertData.downTime,
			insertData.released
        );

        console.log('执行数据库插入');
        const result = await d1SafeRun(insertQuery);
        console.log('添加应用成功:', result.meta);

    } catch (err) {
        console.error('添加应用失败:', {
            package: app.package_name,
            error: err.message,
            stack: err.stack
        });

        // 错误处理也使用 d1SafeRun
        const fallbackInsert = env.DB.prepare(`
            INSERT INTO app_status (package_name, isAlive, lastCheck, belongUs)
            VALUES (?, ?, ?, ?)
        `).bind(
            app.package_name,
            1,  // 默认假设为可用
            now,
            0
        );

        try {
            await d1SafeRun(fallbackInsert);
            console.log('回退插入成功');
        } catch (fallbackErr) {
            console.error('回退插入失败:', fallbackErr.message);
        }
    }
}

async function removeTrackedApp(env: Env, package_name: string) {
    console.log(`移除应用: ${package_name}`);
    await env.APPS_STORE_LIST_AND.delete(package_name);
}

// 监控核心逻辑
async function executeMonitor(env: Env) {
    console.log('执行监控任务');
    const apps = await getAllTrackedApps(env);
    console.log(`找到 ${apps.length} 个应用`);

    for (const app of apps) {
        try {
            await checkAppWithBackoff(app, env);
            await new Promise(resolve => setTimeout(resolve, 1000)); // 间隔1秒
        } catch (err) {
            console.error(`检查 ${app.app_name} 失败:`, err);
        }
    }
    console.log('监控任务完成');
}

async function checkAppWithBackoff(app: AppConfig, env: Env) {
    console.log(`检查应用: ${app.app_name} (${app.package_name})`);
    const now = new Date().toISOString();
    let details: PlayStoreResponse;

    try {
        // 获取应用详情
        details = await checkPlayStore(env.API_KEY, app.package_name);
        const currentStatus = details.status === 'available';
        console.log(`应用状态: ${currentStatus ? '在架' : '下架'}`);

        // 查询当前状态
        const query = env.DB.prepare("SELECT * FROM app_status WHERE package_name = ?").bind(app.package_name);
        const result = await d1SafeRun(query);

        const prevStatus = result.results[0] || null;
        const prevAlive = prevStatus ? Boolean(prevStatus.isAlive) : true; // 如果之前没有记录，默认是在架
        console.log(`之前状态: ${prevAlive ? '在架' : '下架'}`);

        // 确定下架时间
        let downTime = prevStatus?.downTime || null;
        if (prevAlive && !currentStatus) {
            downTime = now; // 刚下架
            console.log('应用刚下架');
        } else if (!prevAlive && currentStatus) {
            downTime = null; // 重新上架
            console.log('应用重新上架');
        }

        // 准备数据，使用 truncateField 确保长度
        const data = {
            title: truncateField(details.title, 255),
            appId: truncateField(details.appId, 100),
            devId: truncateField(details.developer?.devId, 100),
            developerId: truncateField(details.developerId, 100),
            developerEmail: truncateField(details.developerEmail, 255),
            developerWebsite: truncateField(details.developerWebsite, 255),
            developerLegalName: truncateField(details.developerLegalName, 255),
            developerLegalEmail: truncateField(details.developerLegalEmail, 255),
            developerLegalAddress: truncateField(details.developerLegalAddress, 500),
            developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
            developerInternalID: truncateField(details.developerInternalID, 100),
            isAlive: currentStatus ? 1 : 0,
            lastCheck: now,
            downTime: downTime,
			released: truncateField(details.released, 50)
        };

        // 根据是否存在记录选择插入或更新
        let dbQuery;
        if (prevStatus) {
            // 存在记录，执行更新
            dbQuery = env.DB.prepare(`
                UPDATE app_status SET
                    isAlive = ?,
                    lastCheck = ?,
                    downTime = ?,
					released = ?
                WHERE package_name = ?
            `).bind(
                data.isAlive,
                data.lastCheck,
                data.downTime,
				data.released,
                app.package_name
            );
        } else {
            // 不存在记录，执行插入
            dbQuery = env.DB.prepare(`
                INSERT INTO app_status (
                    package_name,
                    title,
                    appId,
                    devId,
                    developerId,
                    developerEmail,
                    developerWebsite,
                    developerLegalName,
                    developerLegalEmail,
                    developerLegalAddress,
                    developerLegalPhoneNumber,
                    developerInternalID,
                    isAlive,
                    lastCheck,
                    downTime,
                    belongUs,
					released
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `).bind(
                app.package_name,
                data.title,
                data.appId,
                data.devId,
                data.developerId,
                data.developerEmail,
                data.developerWebsite,
                data.developerLegalName,
                data.developerLegalEmail,
                data.developerLegalAddress,
                data.developerLegalPhoneNumber,
                data.developerInternalID,
                data.isAlive,
                data.lastCheck,
                data.downTime,
                0, // belongUs默认为0
				data.released,
            );
        }

        // 安全执行数据库操作
        console.log(prevStatus ? '更新应用状态' : '插入新应用状态');
        const dbResult = await d1SafeRun(dbQuery);
        console.log('数据库操作成功:', dbResult.meta);

        // 处理状态变化
        if (prevAlive !== currentStatus) {
            console.log('状态变化，发送通知');
            await sendStatusChangeAlert(
                env,
                app,
                currentStatus ? 'active' : 'taken_down',
                details,
                downTime
            );
        }
		// 新增: 检查是否是新应用（数据库无记录且应用在架）
		if (!prevStatus && currentStatus) {
			console.log('新应用上架，发送通知');
			await sendStatusChangeAlert(
				env,
				app,
				'new_active', // 新应用上架状态通知类型
				details,
				null // 新应用没有下架时间
			);
		}

    } catch (err) {
        console.error(`检查 ${app.app_name} (${app.package_name}) 失败:`, {
            error: err.message,
            stack: err.stack
        });

        // 更新最后检查时间
        const updateQuery = env.DB.prepare(`
            UPDATE app_status SET
                lastCheck = ?
            WHERE package_name = ?
        `).bind(now, app.package_name);

        try {
            await d1SafeRun(updateQuery);
            console.log('更新最后检查时间成功');
        } catch (updateErr) {
            console.error('更新最后检查时间失败:', updateErr.message);
        }
    }
}

function formatToBeiJingTime(isoString: string): string {
    const date = new Date(isoString);
    return new Date(date.getTime())
        .toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
}

async function sendStatusChangeAlert(
    env: Env,
    app: AppConfig,
    status: 'active' | 'taken_down' | 'new_active',
    details: PlayStoreResponse,
    downTime?: string | null
) {
    const action = status === 'active' ? '恢复上架' :
		status === 'taken_down' ? '下架' : '新上架';
    const emoji = status === 'active' ? '🎉' :
		status === 'taken_down' ? '❌' : '🆕';
    const now = formatToBeiJingTime(new Date().toISOString());

    // 安全获取数据库记录
    let dbRecord: any = null;
    try {
        const query = env.DB.prepare("SELECT * FROM app_status WHERE package_name = ?").bind(app.package_name);
        const result = await d1SafeRun(query);
        dbRecord = result.results?.[0] || null;
    } catch (err) {
        console.error(`获取 ${app.package_name} 的数据库记录失败:`, err);
    }

    // 统一的安全字段获取函数
    const getField = (fieldName: string, maxLength: number, defaultValue: string = '未知') => {
        // 优先从数据库获取字段值（如果存在）
        if (dbRecord && dbRecord[fieldName]) {
            return typeof dbRecord[fieldName] === 'string'
                ? dbRecord[fieldName].substring(0, maxLength)
                : dbRecord[fieldName].toString().substring(0, maxLength);
        }

        // 其次从API响应中获取
        if (details && (details as any)[fieldName]) {
            const value = (details as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        // 最后尝试从开发者对象获取
        if (details && details.developer && (details.developer as any)[fieldName]) {
            const value = (details.developer as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        return defaultValue;
    };

    // 构建完整的开发者信息部分
    const buildDeveloperInfo = () => {
        let info = `开发者信息\n-------------\n`;

        // 获取所有可用的开发者字段
        const devId = getField('devId', 100, '未知');
        const legalName = getField('developerLegalName', 255, '未知');

        info += `• 开发者ID: ${devId}\n`;
        info += `• 主体名称: ${legalName}\n`;

        return info;
    };

    let message = `${emoji} 应用${action}\n\n`;
    message += `应用信息\n-------------\n`;
    message += `名称：${app.app_name}\n`;
    message += `包名：${app.package_name}\n`;

    // 添加应用标题（如果可用）
    const title = getField('title', 255, '');
    if (title !== '') {
        message += `Google Play标题：${title}\n`;
    }

    message += `\n`;

    // 添加开发者信息
    message += buildDeveloperInfo();

	// 时间信息
	const released = getField('released', 50, '未知');
	if (status !== 'new_active') {
    	message += `\n时间信息\n-------------\n`;
		message += `上架时间：${released}\n`;

		if (status === 'taken_down' && released) {
			message += `上架时间：${released}\n`;
		} else if (status === 'active' && downTime) {
			message += `上架时间：${released}\n`;
			message += `上次下架：${formatToBeiJingTime(downTime)}\n`;
		}
	}

    // 添加Play Store链接（如果可用）
    const playStoreUrl = getField('playstoreUrl', 255, '');
    if (playStoreUrl !== '') {
        message += `\nPlay Store链接: ${playStoreUrl}\n`;
    }

    await sendLarkAlert(env, app, message);
}

async function checkPlayStore(apiKey: string, packageName: string): Promise<PlayStoreResponse> {
    const url = `https://api-test.kasipesa.com/gp-api/api/apps/${packageName}?country=tz`;
    const res = await fetch(url, {
        method: 'GET',
        headers: { 'x-api-key': apiKey }
    });

    if (res.ok) {
        const data = await res.json();
        return {
            status: 'available',
            ...(data || {}) // 包含所有返回的字段
        };
    }

    if (res.status === 400) {
        return { status: 'unavailable' };
    }

    const responseText = await res.text();
    throw new Error(`API请求失败: ${res.status} ${res.statusText}, URL: ${url}, Key: ${apiKey}, Body: ${responseText}`);
}

async function sendLarkAlert(env: Env, app: AppConfig, message: string) {
    const webhookUrl = env.LARK_WEBHOOK_URL;

    // 构建富文本消息
    const requestBody = {
        msg_type: "post",
        content: {
            post: {
                zh_cn: {
                    title: `【安卓】${app.app_name} 状态更新`,
                    content: [
                        [
                            {
                                tag: "text",
                                text: message
                            }
                        ]
                    ]
                }
            }
        }
    };

    console.log('Lark request URL:', webhookUrl);
    console.log('Lark request body:', JSON.stringify(requestBody, null, 2));

    try {
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        });

        const responseText = await response.text();
        if (!response.ok) {
            throw new Error(`Lark API error: ${response.status} ${response.statusText}\nResponse: ${responseText}`);
        }

        const result = JSON.parse(responseText);
        console.log('Lark message sent successfully:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('Failed to send Lark message:', error);
        throw error;
    }
}
